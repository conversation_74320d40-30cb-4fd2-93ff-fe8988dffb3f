<script lang="ts">
	import { cn } from "$lib/utils/cn.js";
	import { focusRing, hoverTransform } from "$lib/utils/cn.js";
	import Icon from "./Icon.svelte";
	import type { ButtonProps } from "$lib/types/index.js";

	/**
	 * Button component props with comprehensive type safety
	 */
	let {
		variant = "primary",
		size = "md",
		disabled = false,
		loading = false,
		href,
		target = "_self",
		onclick,
		class: className,
		children,
		...restProps
	}: ButtonProps & { children?: any } = $props();

	/**
	 * Base button styles following design system
	 */
	const baseStyles =
		"inline-flex items-center justify-center gap-2 font-semibold rounded-lg transition-all duration-300 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none";

	/**
	 * Variant-specific styling with premium brand colors
	 */
	const variantStyles = {
		primary:
			"bg-earth-600 text-white hover:bg-earth-700 active:bg-earth-800 shadow-md hover:shadow-lg",
		secondary:
			"bg-gold-500 text-white hover:bg-gold-600 active:bg-gold-700 shadow-md hover:shadow-lg",
		outline:
			"border-2 border-earth-600 text-earth-600 hover:bg-earth-600 hover:text-white active:bg-earth-700",
		ghost: "text-earth-600 hover:bg-earth-50 active:bg-earth-100"
	};

	/**
	 * Size-specific styling for consistent spacing
	 */
	const sizeStyles = {
		sm: "px-4 py-2 text-sm",
		md: "px-6 py-3 text-base",
		lg: "px-8 py-4 text-lg"
	};

	/**
	 * Compute final button classes with proper precedence
	 */
	$: buttonClasses = cn(
		baseStyles,
		variantStyles[variant],
		sizeStyles[size],
		focusRing("primary"),
		!disabled && !loading && hoverTransform("lift"),
		className
	);

	/**
	 * Handle button click events with proper event delegation
	 * @param event - Mouse click event
	 */
	function handleClick(event: MouseEvent) {
		// Prevent action if disabled or loading
		if (disabled || loading) {
			event.preventDefault();
			return;
		}

		// Call custom click handler if provided
		if (onclick) {
			onclick(event);
		}
	}

	/**
	 * Determine if button should render as link or button element
	 */
	$: isLink = href && !disabled && !loading;

	/**
	 * Compute ARIA attributes for accessibility
	 */
	$: ariaAttributes = {
		"aria-disabled": disabled || loading,
		"aria-busy": loading,
		"data-testid": restProps["data-testid"] || "button"
	};
</script>

<!--
  Premium Button Component for St. Andrews Towers
  
  A highly reusable, accessible button component with multiple variants,
  sizes, and states. Designed for enterprise-level applications with
  comprehensive prop validation and WCAG 2.1 AA compliance.
  
  @component
  @example
  ```svelte
  <!-- Primary action button -->
<Button variant="primary" size="lg" onclick={handleSubmit}>Get Started</Button>

<!-- Secondary button with icon -->
<Button variant="secondary" size="md" href="/about">
	<Icon name="arrow-right" size="sm" />
	Learn More
</Button>

<!-- Loading state -->
<Button variant="primary" loading disabled>Processing...</Button>
``` <AUTHOR> Andrews Development Team @version 1.0.0 -->

<!--
  Conditional rendering based on whether button should be a link or button element
  Ensures proper semantic HTML and accessibility
-->
{#if isLink}
	<!-- Link variant for navigation -->
	<a {href} {target} class={buttonClasses} onclick={handleClick} {...ariaAttributes} {...restProps}>
		{#if loading}
			<!-- Loading state with spinner -->
			<Icon
				name="loader"
				size={size === "sm" ? "xs" : size === "lg" ? "md" : "sm"}
				class="animate-spin"
			/>
		{/if}

		<!-- Button content slot -->
		{@render children?.()}

		{#if href && target === "_blank"}
			<!-- External link indicator -->
			<Icon name="external-link" size="xs" />
		{/if}
	</a>
{:else}
	<!-- Button element for actions -->
	<button
		type="button"
		class={buttonClasses}
		disabled={disabled || loading}
		onclick={handleClick}
		{...ariaAttributes}
		{...restProps}
	>
		{#if loading}
			<!-- Loading state with spinner -->
			<Icon
				name="loader"
				size={size === "sm" ? "xs" : size === "lg" ? "md" : "sm"}
				class="animate-spin"
			/>
		{/if}

		<!-- Button content slot -->
		{@render children?.()}
	</button>
{/if}

<style>
	/*
	 * Custom styles for enhanced button interactions
	 * Using CSS custom properties for consistent theming
	 */

	/* Smooth transform animations */
	:global(.btn-transform) {
		transform-origin: center;
		transition: transform 0.2s ease-out;
	}

	/* Loading spinner animation */
	:global(.animate-spin) {
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	/* Focus visible styles for keyboard navigation */
	button:focus-visible,
	a:focus-visible {
		outline: 2px solid var(--color-earth-500);
		outline-offset: 2px;
	}

	/* Disabled state styling */
	button:disabled,
	a[aria-disabled="true"] {
		pointer-events: none;
		opacity: 0.5;
	}

	/* High contrast mode support */
	@media (prefers-contrast: high) {
		button,
		a {
			border: 2px solid currentColor;
		}
	}

	/* Reduced motion support */
	@media (prefers-reduced-motion: reduce) {
		button,
		a {
			transition: none;
		}

		:global(.animate-spin) {
			animation: none;
		}
	}
</style>
