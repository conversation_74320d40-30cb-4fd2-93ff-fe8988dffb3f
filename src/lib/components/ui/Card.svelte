<!--
  Premium Card Component for St. Andrews Towers
  
  A flexible, reusable card component with multiple variants and
  comprehensive accessibility features. Designed for enterprise
  applications with proper semantic structure and WCAG compliance.
  
  @component
  @example
  ```svelte
  <!-- Basic card -->
  <Card>
    <h3>Card Title</h3>
    <p>Card content goes here...</p>
  </Card>
  
  <!-- Interactive card with hover effects -->
  <Card variant="interactive" padding="lg">
    <Icon name="building" size="lg" />
    <h3>St. Andrews Towers</h3>
    <p>Premium mixed-use development</p>
  </Card>
  
  <!-- Elevated card for important content -->
  <Card variant="elevated" hover>
    <img src="/image.jpg" alt="Description" />
    <div class="p-6">
      <h3>Featured Content</h3>
    </div>
  </Card>
  ```
  
  <AUTHOR> Andrews Development Team
  @version 1.0.0
-->

<script lang="ts">
	import { cn } from '$lib/utils/cn.js';
	import { hoverTransform } from '$lib/utils/cn.js';
	import type { CardProps } from '$lib/types/index.js';

	/**
	 * Card component props with comprehensive type safety
	 */
	let {
		variant = 'default',
		padding = 'md',
		hover = false,
		class: className,
		children,
		...restProps
	}: CardProps & { children?: any } = $props();

	/**
	 * Base card styles following design system
	 */
	const baseStyles = 'bg-white rounded-xl border transition-all duration-300';

	/**
	 * Variant-specific styling with premium aesthetics
	 */
	const variantStyles = {
		default: 'border-neutral-200 shadow-md hover:shadow-lg',
		elevated: 'border-neutral-100 shadow-xl hover:shadow-2xl',
		interactive: 'border-neutral-200 shadow-md hover:shadow-xl cursor-pointer hover:border-earth-300'
	};

	/**
	 * Padding variants for consistent spacing
	 */
	const paddingStyles = {
		none: '',
		sm: 'p-4',
		md: 'p-6',
		lg: 'p-8'
	};

	/**
	 * Compute final card classes with proper precedence
	 */
	const cardClasses = $derived(cn(
		baseStyles,
		variantStyles[variant],
		paddingStyles[padding],
		hover && hoverTransform('lift'),
		variant === 'interactive' && 'focus-within:ring-2 focus-within:ring-earth-500 focus-within:ring-offset-2',
		className
	));

	/**
	 * Determine if card should be focusable for accessibility
	 */
	const isFocusable = $derived(variant === 'interactive');

	/**
	 * Compute ARIA attributes for accessibility
	 */
	const ariaAttributes = $derived({
		role: variant === 'interactive' ? 'button' : undefined,
		tabindex: isFocusable ? 0 : undefined,
		'data-testid': restProps['data-testid'] || 'card'
	});
</script>

<!--
  Card container with conditional interactivity
  Ensures proper semantic HTML and accessibility
-->
<div
	class={cardClasses}
	{...ariaAttributes}
	{...restProps}
>
	<!-- Card content slot -->
	{@render children?.()}
</div>

<style>
	/*
	 * Enhanced card styling with CSS custom properties
	 * Provides consistent theming and smooth interactions
	 */
	
	/* Smooth transform animations */
	div {
		transform-origin: center;
		transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
	}
	
	/* Interactive card focus styles */
	div[role="button"]:focus {
		outline: none;
	}
	
	/* Hover state enhancements */
	div:hover {
		transform: translateY(-2px);
	}
	
	/* High contrast mode support */
	@media (prefers-contrast: high) {
		div {
			border-width: 2px;
			border-color: currentColor;
		}
	}
	
	/* Reduced motion support */
	@media (prefers-reduced-motion: reduce) {
		div {
			transition: none;
			transform: none !important;
		}
		
		div:hover {
			transform: none;
		}
	}
	
	/* Print styles */
	@media print {
		div {
			box-shadow: none;
			border: 1px solid #000;
		}
	}
</style>
