<!--
  Icon Component for St. Andrews Towers
  Provides consistent icon rendering with Lucide icons
  
  @component
  @example
  ```svelte
  <Icon name="home" size="lg" class="text-earth-600" />
  <Icon name="arrow-right" size="sm" />
  <Icon name="users" size="md" color="primary" />
  ```
-->

<script lang="ts">
	import { cn } from '$lib/utils/cn.js';
	import type { BaseProps } from '$lib/types/index.js';
	
	// Lucide icon imports - commonly used icons for the website
	import {
		Home,
		Building,
		Users,
		Heart,
		Star,
		ArrowRight,
		ArrowLeft,
		ArrowUp,
		ArrowDown,
		ChevronRight,
		ChevronLeft,
		ChevronUp,
		ChevronDown,
		Menu,
		X,
		Mail,
		Phone,
		MapPin,
		Calendar,
		Clock,
		Play,
		Pause,
		Volume2,
		VolumeX,
		Download,
		Share,
		ExternalLink,
		Eye,
		EyeOff,
		Search,
		Filter,
		Grid,
		List,
		Image,
		Video,
		FileText,
		Award,
		Target,
		TrendingUp,
		DollarSign,
		<PERSON><PERSON><PERSON>,
		<PERSON><PERSON><PERSON>,
		<PERSON><PERSON>hart,
		Shield,
		Lock,
		Unlock,
		Check,
		AlertCircle,
		Info,
		HelpCircle,
		Settings,
		User,
		UserPlus,
		MessageCircle,
		Send,
		Upload,
		Camera,
		Mic,
		Globe,
		Wifi,
		Smartphone,
		Laptop,
		Monitor,
		Printer,
		Facebook,
		Twitter,
		Instagram,
		Linkedin,
		Youtube,
		Github
	} from 'lucide-svelte';

	/**
	 * Icon component props interface
	 */
	interface IconProps extends BaseProps {
		/** Icon name from Lucide icon set */
		name: string;
		/** Icon size variant */
		size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
		/** Icon color variant */
		color?: 'primary' | 'secondary' | 'neutral' | 'success' | 'warning' | 'error' | 'current';
		/** Custom stroke width */
		strokeWidth?: number;
		/** Whether icon should be clickable */
		clickable?: boolean;
		/** Click handler for clickable icons */
		onclick?: (event: MouseEvent) => void;
	}

	let {
		name,
		size = 'md',
		color = 'current',
		strokeWidth = 2,
		clickable = false,
		onclick,
		class: className,
		...restProps
	}: IconProps = $props();

	/**
	 * Icon component mapping
	 */
	const iconMap = {
		// Navigation & UI
		home: Home,
		building: Building,
		users: Users,
		heart: Heart,
		star: Star,
		'arrow-right': ArrowRight,
		'arrow-left': ArrowLeft,
		'arrow-up': ArrowUp,
		'arrow-down': ArrowDown,
		'chevron-right': ChevronRight,
		'chevron-left': ChevronLeft,
		'chevron-up': ChevronUp,
		'chevron-down': ChevronDown,
		menu: Menu,
		x: X,
		
		// Contact & Communication
		mail: Mail,
		phone: Phone,
		'map-pin': MapPin,
		calendar: Calendar,
		clock: Clock,
		'message-circle': MessageCircle,
		send: Send,
		
		// Media & Content
		play: Play,
		pause: Pause,
		volume2: Volume2,
		'volume-x': VolumeX,
		download: Download,
		share: Share,
		'external-link': ExternalLink,
		eye: Eye,
		'eye-off': EyeOff,
		search: Search,
		filter: Filter,
		grid: Grid,
		list: List,
		image: Image,
		video: Video,
		'file-text': FileText,
		upload: Upload,
		camera: Camera,
		mic: Mic,
		
		// Business & Analytics
		award: Award,
		target: Target,
		'trending-up': TrendingUp,
		'dollar-sign': DollarSign,
		'pie-chart': PieChart,
		'bar-chart': BarChart,
		'line-chart': LineChart,
		
		// Security & Status
		shield: Shield,
		lock: Lock,
		unlock: Unlock,
		check: Check,
		'alert-circle': AlertCircle,
		info: Info,
		'help-circle': HelpCircle,
		
		// User & Settings
		settings: Settings,
		user: User,
		'user-plus': UserPlus,
		
		// Technology
		globe: Globe,
		wifi: Wifi,
		smartphone: Smartphone,
		laptop: Laptop,
		monitor: Monitor,
		printer: Printer,
		
		// Social Media
		facebook: Facebook,
		twitter: Twitter,
		instagram: Instagram,
		linkedin: Linkedin,
		youtube: Youtube,
		github: Github
	};

	/**
	 * Size class mapping
	 */
	const sizeClasses = {
		xs: 'w-3 h-3',
		sm: 'w-4 h-4',
		md: 'w-5 h-5',
		lg: 'w-6 h-6',
		xl: 'w-8 h-8',
		'2xl': 'w-10 h-10'
	};

	/**
	 * Color class mapping
	 */
	const colorClasses = {
		primary: 'text-earth-600',
		secondary: 'text-gold-500',
		neutral: 'text-neutral-600',
		success: 'text-green-600',
		warning: 'text-yellow-600',
		error: 'text-red-600',
		current: 'text-current'
	};

	/**
	 * Get the icon component from the map
	 */
	$: IconComponent = iconMap[name as keyof typeof iconMap];

	/**
	 * Compute final classes
	 */
	$: classes = cn(
		'inline-block',
		sizeClasses[size],
		colorClasses[color],
		clickable && 'cursor-pointer hover:opacity-75 transition-opacity',
		className
	);

	/**
	 * Handle click events
	 */
	function handleClick(event: MouseEvent) {
		if (clickable && onclick) {
			onclick(event);
		}
	}
</script>

<!-- 
  Render the icon component if it exists, otherwise show a fallback
-->
{#if IconComponent}
	<svelte:component
		this={IconComponent}
		class={classes}
		stroke-width={strokeWidth}
		onclick={handleClick}
		{...restProps}
	/>
{:else}
	<!-- Fallback for unknown icons -->
	<div
		class={cn(
			'inline-flex items-center justify-center border border-dashed border-neutral-300 text-neutral-400 text-xs font-mono',
			sizeClasses[size],
			className
		)}
		title="Icon not found: {name}"
		{...restProps}
	>
		?
	</div>
{/if}
