<!--
  ScrollReveal Animation Component for St. Andrews Towers
  
  Advanced scroll-triggered animations with multiple effects and
  intersection observer optimization. Provides smooth, performance-optimized
  animations that enhance user experience without impacting performance.
  
  @component
  @example
  ```svelte
  <!-- Scale in animation -->
  <ScrollReveal animation="scaleIn" threshold={0.3}>
    <Card>Scales in when 30% visible</Card>
  </ScrollReveal>
  
  <!-- Custom GSAP animation -->
  <ScrollReveal 
    animation="custom"
    customAnimation={(element) => gsap.from(element, {
      rotationY: 90,
      duration: 1,
      ease: "back.out(1.7)"
    })}
  >
    <div>Custom 3D rotation</div>
  </ScrollReveal>
  
  <!-- Staggered children animation -->
  <ScrollReveal animation="fadeIn" stagger={0.15}>
    <div class="grid grid-cols-2 gap-4">
      <div>Item 1</div>
      <div>Item 2</div>
      <div>Item 3</div>
      <div>Item 4</div>
    </div>
  </ScrollReveal>
  ```
  
  <AUTHOR> Andrews Development Team
  @version 1.0.0
-->

<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { 
		fadeIn, 
		scaleIn, 
		staggerAnimation, 
		scrollTriggerAnimation,
		ANIMATION_CONFIG 
	} from '$lib/utils/gsap.js';
	import { cn } from '$lib/utils/cn.js';
	import type { BaseProps } from '$lib/types/index.js';

	/**
	 * ScrollReveal animation component props
	 */
	interface ScrollRevealProps extends BaseProps {
		/** Animation type */
		animation?: 'fadeIn' | 'scaleIn' | 'slideUp' | 'slideLeft' | 'slideRight' | 'custom';
		/** Animation duration in seconds */
		duration?: number;
		/** Animation delay in seconds */
		delay?: number;
		/** Animation easing function */
		ease?: string;
		/** Stagger timing for child elements */
		stagger?: number;
		/** Intersection observer threshold (0-1) */
		threshold?: number;
		/** Whether to trigger animation only once */
		once?: boolean;
		/** Custom animation function for 'custom' type */
		customAnimation?: (element: Element) => any;
		/** Scroll trigger start position */
		triggerStart?: string;
		/** Scroll trigger end position */
		triggerEnd?: string;
		/** Children content */
		children?: any;
	}

	let {
		animation = 'fadeIn',
		duration = ANIMATION_CONFIG.durations.normal,
		delay = 0,
		ease = ANIMATION_CONFIG.easings.smooth,
		stagger = 0,
		threshold = 0.1,
		once = true,
		customAnimation,
		triggerStart = 'top 85%',
		triggerEnd = 'bottom 15%',
		class: className,
		children,
		...restProps
	}: ScrollRevealProps = $props();

	/**
	 * Component element reference
	 */
	let containerRef: HTMLDivElement;
	let scrollTriggerInstance: any;
	let intersectionObserver: IntersectionObserver;

	/**
	 * Animation factory function
	 * Creates appropriate animation based on type
	 */
	function createAnimation(element: Element): any {
		const options = { duration, delay, ease };

		switch (animation) {
			case 'fadeIn':
				return fadeIn(element, { ...options, direction: 'up' });
			
			case 'scaleIn':
				return scaleIn(element, options);
			
			case 'slideUp':
				return fadeIn(element, { ...options, direction: 'up', distance: 50 });
			
			case 'slideLeft':
				return fadeIn(element, { ...options, direction: 'left', distance: 50 });
			
			case 'slideRight':
				return fadeIn(element, { ...options, direction: 'right', distance: 50 });
			
			case 'custom':
				return customAnimation ? customAnimation(element) : fadeIn(element, options);
			
			default:
				return fadeIn(element, options);
		}
	}

	/**
	 * Initialize animations with intersection observer optimization
	 */
	function initializeAnimation() {
		if (!containerRef || typeof window === 'undefined') return;

		// Create animation timeline
		let animationTimeline;

		if (stagger > 0) {
			// Staggered animation for child elements
			const children = containerRef.children;
			if (children.length > 0) {
				animationTimeline = staggerAnimation(
					children,
					(element) => createAnimation(element),
					stagger
				);
			}
		} else {
			// Single element animation
			animationTimeline = createAnimation(containerRef);
		}

		// Set up scroll trigger
		if (animationTimeline) {
			scrollTriggerInstance = scrollTriggerAnimation(
				containerRef,
				animationTimeline,
				{
					start: triggerStart,
					end: triggerEnd,
					toggleActions: once ? 'play none none none' : 'play none none reverse'
				}
			);
		}
	}

	/**
	 * Intersection Observer for performance optimization
	 * Only initialize animations when element is near viewport
	 */
	function setupIntersectionObserver() {
		if (typeof window === 'undefined' || !containerRef) return;

		intersectionObserver = new IntersectionObserver(
			(entries) => {
				entries.forEach((entry) => {
					if (entry.isIntersecting && entry.intersectionRatio >= threshold) {
						// Element is visible, initialize animation
						initializeAnimation();
						// Disconnect observer after first trigger if once=true
						if (once) {
							intersectionObserver.disconnect();
						}
					}
				});
			},
			{
				threshold,
				rootMargin: '50px 0px'
			}
		);

		intersectionObserver.observe(containerRef);
	}

	/**
	 * Component lifecycle
	 */
	onMount(() => {
		setupIntersectionObserver();
	});

	onDestroy(() => {
		if (scrollTriggerInstance) {
			scrollTriggerInstance.kill();
		}
		if (intersectionObserver) {
			intersectionObserver.disconnect();
		}
	});

	/**
	 * Container classes with animation data attributes
	 */
	const containerClasses = $derived(cn(
		'scroll-reveal-container',
		className
	));
</script>

<!--
  Animation container with semantic structure and data attributes
-->
<div
	bind:this={containerRef}
	class={containerClasses}
	data-animation={animation}
	data-duration={duration}
	data-stagger={stagger}
	{...restProps}
>
	<!-- Content slot -->
	{@render children?.()}
</div>

<style>
	/*
	 * ScrollReveal container styles
	 * Ensures proper initial state and performance optimization
	 */
	
	.scroll-reveal-container {
		/* Performance optimization */
		will-change: opacity, transform;
		
		/* Ensure container doesn't cause layout shifts */
		contain: layout style;
	}
	
	/* Reduced motion support */
	@media (prefers-reduced-motion: reduce) {
		.scroll-reveal-container {
			/* Disable all animations */
			opacity: 1 !important;
			transform: none !important;
			transition: none !important;
		}
		
		.scroll-reveal-container * {
			opacity: 1 !important;
			transform: none !important;
			transition: none !important;
		}
	}
	
	/* High contrast mode support */
	@media (prefers-contrast: high) {
		.scroll-reveal-container {
			opacity: 1;
		}
	}
	
	/* Print styles */
	@media print {
		.scroll-reveal-container {
			opacity: 1 !important;
			transform: none !important;
		}
		
		.scroll-reveal-container * {
			opacity: 1 !important;
			transform: none !important;
		}
	}
</style>
