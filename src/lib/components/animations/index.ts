/**
 * Animation Components Export Index for St. Andrews Towers
 *
 * Centralized exports for all animation components and utilities.
 * Provides a clean API for importing animation functionality
 * throughout the application.
 *
 * <AUTHOR> Andrews Development Team
 * @version 1.0.0
 */

// Animation Components
export { default as FadeIn } from './FadeIn.svelte';
export { default as ScrollReveal } from './ScrollReveal.svelte';

// Animation Utilities (re-export from utils)
export {
	fadeIn,
	scaleIn,
	staggerAnimation,
	scrollTriggerAnimation,
	parallaxScroll,
	animateCounter,
	textReveal,
	cleanupScrollTriggers,
	ANIMATION_CONFIG
} from '$lib/utils/gsap.js';

// Animation Types
export type {
	AnimationConfig
} from '$lib/types/index.js';

/**
 * Animation presets for common use cases
 */
export const ANIMATION_PRESETS = {
	// Hero section animations
	hero: {
		title: {
			animation: 'fadeIn' as const,
			direction: 'up' as const,
			duration: 0.8,
			delay: 0.2,
			distance: 40
		},
		subtitle: {
			animation: 'fadeIn' as const,
			direction: 'up' as const,
			duration: 0.6,
			delay: 0.4,
			distance: 30
		},
		cta: {
			animation: 'scaleIn' as const,
			duration: 0.5,
			delay: 0.6,
			ease: 'back.out(1.7)'
		}
	},

	// Card grid animations
	cardGrid: {
		container: {
			animation: 'fadeIn' as const,
			stagger: 0.15,
			duration: 0.6
		},
		card: {
			animation: 'fadeIn' as const,
			direction: 'up' as const,
			duration: 0.5,
			distance: 20
		}
	},

	// Statistics/counter animations
	statistics: {
		counter: {
			duration: 2.0,
			ease: 'power2.out',
			formatter: (value: number) => Math.round(value).toLocaleString()
		},
		container: {
			animation: 'fadeIn' as const,
			stagger: 0.2,
			triggerStart: 'top 70%'
		}
	}
} as const;

/**
 * Animation presets for common use cases
 */
export const ANIMATION_PRESETS = {
	// Hero section animations
	hero: {
		title: {
			animation: 'fadeIn' as const,
			direction: 'up' as const,
			duration: 0.8,
			delay: 0.2,
			distance: 40
		},
		subtitle: {
			animation: 'fadeIn' as const,
			direction: 'up' as const,
			duration: 0.6,
			delay: 0.4,
			distance: 30
		},
		cta: {
			animation: 'scaleIn' as const,
			duration: 0.5,
			delay: 0.6,
			ease: 'back.out(1.7)'
		}
	},

	// Card grid animations
	cardGrid: {
		container: {
			animation: 'fadeIn' as const,
			stagger: 0.15,
			duration: 0.6
		},
		card: {
			animation: 'fadeIn' as const,
			direction: 'up' as const,
			duration: 0.5,
			distance: 20
		}
	},

	// Statistics/counter animations
	statistics: {
		counter: {
			duration: 2.0,
			ease: 'power2.out',
			formatter: (value: number) => Math.round(value).toLocaleString()
		},
		container: {
			animation: 'fadeIn' as const,
			stagger: 0.2,
			triggerStart: 'top 70%'
		}
	},

	// Navigation animations
	navigation: {
		mobileMenu: {
			duration: 0.3,
			ease: 'power2.out'
		},
		dropdown: {
			animation: 'fadeIn' as const,
			direction: 'up' as const,
			duration: 0.2,
			distance: 10
		}
	},

	// Modal/overlay animations
	modal: {
		backdrop: {
			duration: 0.3,
			ease: 'power2.out'
		},
		content: {
			animation: 'scaleIn' as const,
			duration: 0.4,
			ease: 'back.out(1.7)',
			scale: 0.9
		}
	},

	// Gallery animations
	gallery: {
		item: {
			animation: 'scaleIn' as const,
			duration: 0.4,
			stagger: 0.1
		},
		lightbox: {
			animation: 'fadeIn' as const,
			duration: 0.3
		}
	},

	// Form animations
	form: {
		field: {
			animation: 'fadeIn' as const,
			direction: 'up' as const,
			duration: 0.4,
			stagger: 0.1,
			distance: 15
		},
		error: {
			animation: 'fadeIn' as const,
			direction: 'left' as const,
			duration: 0.3,
			distance: 10
		}
	}
} as const;

/**
 * Animation utility functions for common patterns
 */
export const animationUtils = {
	/**
	 * Create a staggered fade-in animation for lists
	 */
	staggeredList: (elements: Element[], delay: number = 0.1) => {
		return staggerAnimation(
			elements,
			(element) => fadeIn(element, { 
				direction: 'up', 
				duration: 0.5, 
				distance: 20 
			}),
			delay
		);
	},

	/**
	 * Create a smooth page transition
	 */
	pageTransition: (element: Element, direction: 'in' | 'out' = 'in') => {
		if (direction === 'in') {
			return fadeIn(element, {
				direction: 'up',
				duration: 0.6,
				distance: 30
			});
		} else {
			return fadeIn(element, {
				direction: 'down',
				duration: 0.4,
				distance: 20
			});
		}
	},

	/**
	 * Create a loading animation
	 */
	loading: (element: Element) => {
		return scaleIn(element, {
			duration: 0.8,
			ease: 'elastic.out(1, 0.3)',
			scale: 0.5
		});
	},

	/**
	 * Create a success animation
	 */
	success: (element: Element) => {
		return scaleIn(element, {
			duration: 0.6,
			ease: 'back.out(2)',
			scale: 0.8
		});
	}
};

/**
 * Performance optimization utilities
 */
export const animationPerformance = {
	/**
	 * Check if user prefers reduced motion
	 */
	prefersReducedMotion: (): boolean => {
		if (typeof window === 'undefined') return false;
		return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
	},

	/**
	 * Get optimal animation duration based on user preferences
	 */
	getOptimalDuration: (baseDuration: number): number => {
		return animationPerformance.prefersReducedMotion() ? 0 : baseDuration;
	},

	/**
	 * Create intersection observer for performance
	 */
	createIntersectionObserver: (
		callback: IntersectionObserverCallback,
		options?: IntersectionObserverInit
	): IntersectionObserver | null => {
		if (typeof window === 'undefined') return null;
		
		return new IntersectionObserver(callback, {
			threshold: 0.1,
			rootMargin: '50px 0px',
			...options
		});
	}
};
