<!--
  FadeIn Animation Component for St. Andrews Towers
  
  A reusable animation wrapper that provides smooth fade-in effects
  with optional directional movement. Uses GSAP for performance-optimized
  animations with scroll triggers and intersection observers.
  
  @component
  @example
  ```svelte
  <!-- Basic fade in -->
  <FadeIn>
    <h1>Welcome to St. Andrews Towers</h1>
  </FadeIn>
  
  <!-- Fade in from bottom with custom timing -->
  <FadeIn direction="up" duration={0.8} delay={0.2}>
    <Card>Content here</Card>
  </FadeIn>
  
  <!-- Staggered animation for multiple children -->
  <FadeIn stagger={0.1} triggerOnce={false}>
    <div class="grid grid-cols-3 gap-6">
      <Card>Item 1</Card>
      <Card>Item 2</Card>
      <Card>Item 3</Card>
    </div>
  </FadeIn>
  ```
  
  <AUTHOR> Andrews Development Team
  @version 1.0.0
-->

<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { fadeIn, scrollTriggerAnimation, cleanupScrollTriggers } from '$lib/utils/gsap.js';
	import { cn } from '$lib/utils/cn.js';
	import type { AnimationConfig, BaseProps } from '$lib/types/index.js';

	/**
	 * FadeIn animation component props
	 */
	interface FadeInProps extends BaseProps, AnimationConfig {
		/** Animation direction */
		direction?: 'up' | 'down' | 'left' | 'right' | 'none';
		/** Distance to move during animation */
		distance?: number;
		/** Whether to trigger animation only once */
		triggerOnce?: boolean;
		/** Scroll trigger start position */
		triggerStart?: string;
		/** Scroll trigger end position */
		triggerEnd?: string;
		/** Children content */
		children?: any;
	}

	let {
		direction = 'up',
		distance = 30,
		duration = 0.6,
		delay = 0,
		ease = 'power2.out',
		stagger = 0,
		triggerOnce = true,
		triggerStart = 'top 80%',
		triggerEnd = 'bottom 20%',
		class: className,
		children,
		...restProps
	}: FadeInProps = $props();

	/**
	 * Component element reference
	 */
	let containerRef: HTMLDivElement;
	let scrollTriggerInstance: any;

	/**
	 * Initialize animation on component mount
	 */
	onMount(() => {
		if (!containerRef) return;

		// Create the fade-in animation
		const animation = fadeIn(containerRef, {
			duration,
			delay,
			direction,
			distance,
			ease
		});

		// Set up scroll trigger if in viewport
		if (typeof window !== 'undefined') {
			scrollTriggerInstance = scrollTriggerAnimation(
				containerRef,
				animation,
				{
					start: triggerStart,
					end: triggerEnd,
					toggleActions: triggerOnce ? 'play none none none' : 'play none none reverse'
				}
			);
		}
	});

	/**
	 * Cleanup animations on component destroy
	 */
	onDestroy(() => {
		if (scrollTriggerInstance) {
			scrollTriggerInstance.kill();
		}
	});

	/**
	 * Container classes for proper animation setup
	 */
	const containerClasses = $derived(cn(
		'fade-in-container',
		className
	));
</script>

<!--
  Animation container with proper semantic structure
-->
<div
	bind:this={containerRef}
	class={containerClasses}
	data-animation="fade-in"
	data-direction={direction}
	{...restProps}
>
	<!-- Content slot -->
	{@render children?.()}
</div>

<style>
	/*
	 * Animation container styles
	 * Ensures proper initial state and smooth transitions
	 */
	
	.fade-in-container {
		/* Initial state - will be overridden by GSAP */
		opacity: 0;
		
		/* Ensure container doesn't affect layout during animation */
		will-change: opacity, transform;
	}
	
	/* Reduced motion support */
	@media (prefers-reduced-motion: reduce) {
		.fade-in-container {
			opacity: 1 !important;
			transform: none !important;
			transition: none !important;
		}
	}
	
	/* High contrast mode support */
	@media (prefers-contrast: high) {
		.fade-in-container {
			/* Ensure content is visible in high contrast */
			opacity: 1;
		}
	}
	
	/* Print styles */
	@media print {
		.fade-in-container {
			opacity: 1 !important;
			transform: none !important;
		}
	}
</style>
