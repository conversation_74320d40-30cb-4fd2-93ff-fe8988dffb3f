/**
 * TypeScript interfaces and types for St. Andrews Towers website
 * Provides type safety for components, props, and data structures
 * 
 * <AUTHOR> Andrews Development Team
 * @version 1.0.0
 */

/**
 * Base component props that all components can accept
 */
export interface BaseProps {
	class?: string;
	id?: string;
	'data-testid'?: string;
}

/**
 * Animation configuration types
 */
export interface AnimationConfig {
	duration?: number;
	delay?: number;
	ease?: string;
	stagger?: number;
}

/**
 * Button component variants and sizes
 */
export type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost';
export type ButtonSize = 'sm' | 'md' | 'lg';

export interface ButtonProps extends BaseProps {
	variant?: ButtonVariant;
	size?: ButtonSize;
	disabled?: boolean;
	loading?: boolean;
	href?: string;
	target?: '_blank' | '_self' | '_parent' | '_top';
	onclick?: (event: MouseEvent) => void;
}

/**
 * Card component types
 */
export type CardVariant = 'default' | 'elevated' | 'interactive';

export interface CardProps extends BaseProps {
	variant?: CardVariant;
	padding?: 'none' | 'sm' | 'md' | 'lg';
	hover?: boolean;
}

/**
 * Navigation types
 */
export interface NavItem {
	label: string;
	href: string;
	icon?: string;
	children?: NavItem[];
	external?: boolean;
}

export interface NavigationProps extends BaseProps {
	items: NavItem[];
	currentPath?: string;
	mobile?: boolean;
}

/**
 * Hero section types
 */
export interface HeroProps extends BaseProps {
	title: string;
	subtitle?: string;
	description?: string;
	backgroundImage?: string;
	backgroundVideo?: string;
	ctaText?: string;
	ctaHref?: string;
	secondaryCtaText?: string;
	secondaryCtaHref?: string;
	overlay?: boolean;
	centered?: boolean;
}

/**
 * Statistics/Counter types
 */
export interface Statistic {
	value: number;
	label: string;
	suffix?: string;
	prefix?: string;
	description?: string;
	icon?: string;
}

export interface StatisticsProps extends BaseProps {
	statistics: Statistic[];
	animated?: boolean;
	columns?: 2 | 3 | 4;
}

/**
 * Testimonial types
 */
export interface Testimonial {
	id: string;
	name: string;
	role: string;
	company?: string;
	content: string;
	avatar?: string;
	rating?: number;
	featured?: boolean;
}

export interface TestimonialsProps extends BaseProps {
	testimonials: Testimonial[];
	layout?: 'grid' | 'carousel' | 'masonry';
	showRatings?: boolean;
}

/**
 * Gallery types
 */
export interface GalleryItem {
	id: string;
	src: string;
	alt: string;
	title?: string;
	description?: string;
	category?: string;
	type: 'image' | 'video';
	thumbnail?: string;
}

export interface GalleryProps extends BaseProps {
	items: GalleryItem[];
	layout?: 'grid' | 'masonry';
	categories?: string[];
	lightbox?: boolean;
}

/**
 * Video player types
 */
export interface VideoProps extends BaseProps {
	src: string;
	poster?: string;
	title?: string;
	description?: string;
	autoplay?: boolean;
	muted?: boolean;
	controls?: boolean;
	loop?: boolean;
	aspectRatio?: '16:9' | '4:3' | '1:1';
}

/**
 * Form types
 */
export interface FormField {
	name: string;
	label: string;
	type: 'text' | 'email' | 'tel' | 'textarea' | 'select' | 'checkbox' | 'radio';
	placeholder?: string;
	required?: boolean;
	options?: { value: string; label: string }[];
	validation?: {
		pattern?: string;
		minLength?: number;
		maxLength?: number;
		min?: number;
		max?: number;
	};
}

export interface ContactFormProps extends BaseProps {
	fields: FormField[];
	submitText?: string;
	successMessage?: string;
	errorMessage?: string;
	onSubmit?: (data: Record<string, any>) => Promise<void>;
}

/**
 * Timeline types
 */
export interface TimelineItem {
	id: string;
	date: string;
	title: string;
	description: string;
	status: 'completed' | 'current' | 'upcoming';
	icon?: string;
	image?: string;
}

export interface TimelineProps extends BaseProps {
	items: TimelineItem[];
	orientation?: 'horizontal' | 'vertical';
	animated?: boolean;
}

/**
 * Investment data types
 */
export interface InvestmentMetric {
	label: string;
	value: string | number;
	change?: number;
	trend?: 'up' | 'down' | 'stable';
	description?: string;
}

export interface InvestmentProps extends BaseProps {
	metrics: InvestmentMetric[];
	projections?: {
		year: number;
		revenue: number;
		profit: number;
		roi: number;
	}[];
	riskFactors?: string[];
}

/**
 * Team member types
 */
export interface TeamMember {
	id: string;
	name: string;
	role: string;
	bio: string;
	avatar: string;
	email?: string;
	linkedin?: string;
	twitter?: string;
	expertise?: string[];
}

export interface TeamProps extends BaseProps {
	members: TeamMember[];
	layout?: 'grid' | 'list';
	showContact?: boolean;
}

/**
 * SEO and meta types
 */
export interface SEOData {
	title: string;
	description: string;
	keywords?: string[];
	image?: string;
	url?: string;
	type?: 'website' | 'article';
	author?: string;
	publishedTime?: string;
	modifiedTime?: string;
}

/**
 * Page layout types
 */
export interface PageProps {
	seo: SEOData;
	children?: any;
}

/**
 * API response types
 */
export interface APIResponse<T = any> {
	success: boolean;
	data?: T;
	error?: string;
	message?: string;
}

/**
 * Error types
 */
export interface AppError {
	code: string;
	message: string;
	details?: any;
}

/**
 * Utility types
 */
export type Breakpoint = 'sm' | 'md' | 'lg' | 'xl' | '2xl';
export type ColorVariant = 'primary' | 'secondary' | 'neutral' | 'success' | 'warning' | 'error';
export type Size = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
export type Spacing = 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';

/**
 * Component state types
 */
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface ComponentState {
	loading: LoadingState;
	error?: AppError;
	data?: any;
}
