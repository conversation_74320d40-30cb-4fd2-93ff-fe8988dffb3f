/**
 * GSAP Animation Utilities for St. Andrews Towers
 * Provides reusable animation functions with consistent timing and easing
 * 
 * <AUTHOR> Andrews Development Team
 * @version 1.0.0
 */

import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
if (typeof window !== 'undefined') {
	gsap.registerPlugin(ScrollTrigger);
}

/**
 * Animation configuration constants
 */
export const ANIMATION_CONFIG = {
	durations: {
		fast: 0.3,
		normal: 0.6,
		slow: 1.0,
		slower: 1.5
	},
	easings: {
		smooth: 'power2.out',
		bounce: 'back.out(1.7)',
		elastic: 'elastic.out(1, 0.3)',
		expo: 'expo.out'
	},
	stagger: {
		fast: 0.1,
		normal: 0.2,
		slow: 0.3
	}
} as const;

/**
 * Fade in animation with optional direction
 * 
 * @param element - Target element(s)
 * @param options - Animation options
 * @returns GSAP timeline
 */
export function fadeIn(
	element: gsap.TweenTarget,
	options: {
		duration?: number;
		delay?: number;
		direction?: 'up' | 'down' | 'left' | 'right' | 'none';
		distance?: number;
		ease?: string;
	} = {}
) {
	const {
		duration = ANIMATION_CONFIG.durations.normal,
		delay = 0,
		direction = 'up',
		distance = 30,
		ease = ANIMATION_CONFIG.easings.smooth
	} = options;

	const tl = gsap.timeline();
	
	// Set initial state
	const initialState: gsap.TweenVars = { opacity: 0 };
	
	if (direction !== 'none') {
		switch (direction) {
			case 'up':
				initialState.y = distance;
				break;
			case 'down':
				initialState.y = -distance;
				break;
			case 'left':
				initialState.x = distance;
				break;
			case 'right':
				initialState.x = -distance;
				break;
		}
	}
	
	gsap.set(element, initialState);
	
	// Animate to final state
	const finalState: gsap.TweenVars = {
		opacity: 1,
		duration,
		delay,
		ease
	};
	
	if (direction !== 'none') {
		finalState.x = 0;
		finalState.y = 0;
	}
	
	tl.to(element, finalState);
	
	return tl;
}

/**
 * Scale in animation with bounce effect
 * 
 * @param element - Target element(s)
 * @param options - Animation options
 * @returns GSAP timeline
 */
export function scaleIn(
	element: gsap.TweenTarget,
	options: {
		duration?: number;
		delay?: number;
		scale?: number;
		ease?: string;
	} = {}
) {
	const {
		duration = ANIMATION_CONFIG.durations.normal,
		delay = 0,
		scale = 0.8,
		ease = ANIMATION_CONFIG.easings.bounce
	} = options;

	gsap.set(element, { scale, opacity: 0 });
	
	return gsap.to(element, {
		scale: 1,
		opacity: 1,
		duration,
		delay,
		ease
	});
}

/**
 * Staggered animation for multiple elements
 * 
 * @param elements - Target elements
 * @param animation - Animation function to apply
 * @param stagger - Stagger timing
 * @returns GSAP timeline
 */
export function staggerAnimation(
	elements: gsap.TweenTarget,
	animation: (element: Element, index: number) => gsap.core.Timeline | gsap.core.Tween,
	stagger: number = ANIMATION_CONFIG.stagger.normal
) {
	const tl = gsap.timeline();
	
	gsap.utils.toArray(elements).forEach((element, index) => {
		tl.add(animation(element as Element, index), index * stagger);
	});
	
	return tl;
}

/**
 * Scroll-triggered animation
 * 
 * @param element - Target element
 * @param animation - Animation to trigger
 * @param options - ScrollTrigger options
 * @returns ScrollTrigger instance
 */
export function scrollTriggerAnimation(
	element: gsap.TweenTarget,
	animation: gsap.core.Timeline | gsap.core.Tween,
	options: ScrollTrigger.Vars = {}
) {
	return ScrollTrigger.create({
		trigger: element,
		start: 'top 80%',
		end: 'bottom 20%',
		animation,
		toggleActions: 'play none none reverse',
		...options
	});
}

/**
 * Parallax scroll effect
 * 
 * @param element - Target element
 * @param speed - Parallax speed (0-1, where 0.5 is half speed)
 * @returns ScrollTrigger instance
 */
export function parallaxScroll(
	element: gsap.TweenTarget,
	speed: number = 0.5
) {
	return gsap.to(element, {
		yPercent: -50 * speed,
		ease: 'none',
		scrollTrigger: {
			trigger: element,
			start: 'top bottom',
			end: 'bottom top',
			scrub: true
		}
	});
}

/**
 * Counter animation with easing
 * 
 * @param element - Target element containing the number
 * @param endValue - Final number value
 * @param options - Animation options
 * @returns GSAP tween
 */
export function animateCounter(
	element: Element,
	endValue: number,
	options: {
		duration?: number;
		startValue?: number;
		ease?: string;
		formatter?: (value: number) => string;
	} = {}
) {
	const {
		duration = ANIMATION_CONFIG.durations.slow,
		startValue = 0,
		ease = ANIMATION_CONFIG.easings.smooth,
		formatter = (value: number) => Math.round(value).toString()
	} = options;

	const obj = { value: startValue };
	
	return gsap.to(obj, {
		value: endValue,
		duration,
		ease,
		onUpdate: () => {
			element.textContent = formatter(obj.value);
		}
	});
}

/**
 * Morphing text reveal animation
 * 
 * @param element - Target text element
 * @param options - Animation options
 * @returns GSAP timeline
 */
export function textReveal(
	element: gsap.TweenTarget,
	options: {
		duration?: number;
		stagger?: number;
		ease?: string;
	} = {}
) {
	const {
		duration = ANIMATION_CONFIG.durations.normal,
		stagger = ANIMATION_CONFIG.stagger.fast,
		ease = ANIMATION_CONFIG.easings.smooth
	} = options;

	const tl = gsap.timeline();
	
	// Split text into characters or words
	const chars = gsap.utils.toArray(`${element} .char, ${element} .word`);
	
	gsap.set(chars, { opacity: 0, y: 20 });
	
	tl.to(chars, {
		opacity: 1,
		y: 0,
		duration,
		ease,
		stagger
	});
	
	return tl;
}

/**
 * Cleanup function for ScrollTrigger instances
 * Call this when component unmounts
 */
export function cleanupScrollTriggers() {
	ScrollTrigger.getAll().forEach(trigger => trigger.kill());
}
