/**
 * Utility function for merging Tailwind CSS classes with proper precedence
 * Uses clsx for conditional classes and tailwind-merge for deduplication
 * 
 * @param inputs - Class names, objects, or arrays to merge
 * @returns Merged and deduplicated class string
 * 
 * @example
 * cn('px-4 py-2', 'bg-blue-500', { 'text-white': true })
 * // Returns: 'px-4 py-2 bg-blue-500 text-white'
 * 
 * cn('px-4', 'px-6') // Later class wins
 * // Returns: 'px-6'
 */

import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

/**
 * Utility function for creating responsive class variants
 * 
 * @param base - Base classes to apply
 * @param variants - Responsive variants object
 * @returns Merged responsive classes
 * 
 * @example
 * responsive('text-base', {
 *   sm: 'text-lg',
 *   md: 'text-xl',
 *   lg: 'text-2xl'
 * })
 * // Returns: 'text-base sm:text-lg md:text-xl lg:text-2xl'
 */
export function responsive(
	base: string,
	variants: Partial<Record<'sm' | 'md' | 'lg' | 'xl' | '2xl', string>>
) {
	const classes = [base];
	
	Object.entries(variants).forEach(([breakpoint, className]) => {
		if (className) {
			classes.push(`${breakpoint}:${className}`);
		}
	});
	
	return classes.join(' ');
}

/**
 * Utility function for creating animation delay classes
 * 
 * @param index - Index for staggered animations
 * @param baseDelay - Base delay in milliseconds (default: 100)
 * @returns Animation delay class
 * 
 * @example
 * animationDelay(0) // Returns: 'animation-delay-0'
 * animationDelay(1) // Returns: 'animation-delay-100'
 * animationDelay(2, 150) // Returns: 'animation-delay-300'
 */
export function animationDelay(index: number, baseDelay: number = 100): string {
	const delay = index * baseDelay;
	return `[animation-delay:${delay}ms]`;
}

/**
 * Utility function for creating focus ring classes with brand colors
 * 
 * @param color - Color variant ('primary' | 'secondary' | 'neutral')
 * @returns Focus ring classes
 */
export function focusRing(color: 'primary' | 'secondary' | 'neutral' = 'primary'): string {
	const rings = {
		primary: 'focus:ring-2 focus:ring-earth-500 focus:ring-offset-2',
		secondary: 'focus:ring-2 focus:ring-gold-400 focus:ring-offset-2',
		neutral: 'focus:ring-2 focus:ring-neutral-400 focus:ring-offset-2'
	};
	
	return rings[color];
}

/**
 * Utility function for creating hover transform classes
 * 
 * @param type - Transform type ('lift' | 'scale' | 'none')
 * @returns Hover transform classes
 */
export function hoverTransform(type: 'lift' | 'scale' | 'none' = 'lift'): string {
	const transforms = {
		lift: 'hover:-translate-y-1 hover:shadow-lg',
		scale: 'hover:scale-105',
		none: ''
	};
	
	return transforms[type];
}
