/**
 * Design System Constants for St. Andrews Towers
 * Centralized design tokens and configuration values
 * 
 * <AUTHOR> Andrews Development Team
 * @version 1.0.0
 */

/**
 * Brand color palette inspired by African heritage and women empowerment
 */
export const COLORS = {
	// African Heritage Earth Tones
	earth: {
		50: '#fdf8f3',
		100: '#f9ede0',
		200: '#f2d7bf',
		300: '#e8bb94',
		400: '#dc9567',
		500: '#d17a47',
		600: '#c3633c',
		700: '#a24f34',
		800: '#834131',
		900: '#6b3729'
	},
	
	// Premium Gold Accents
	gold: {
		50: '#fffbeb',
		100: '#fef3c7',
		200: '#fde68a',
		300: '#fcd34d',
		400: '#fbbf24',
		500: '#f59e0b',
		600: '#d97706',
		700: '#b45309',
		800: '#92400e',
		900: '#78350f'
	},
	
	// Women Empowerment Purple
	purple: {
		50: '#faf5ff',
		100: '#f3e8ff',
		200: '#e9d5ff',
		300: '#d8b4fe',
		400: '#c084fc',
		500: '#a855f7',
		600: '#9333ea',
		700: '#7c3aed',
		800: '#6b21a8',
		900: '#581c87'
	},
	
	// Modern Neutrals
	neutral: {
		50: '#fafafa',
		100: '#f5f5f5',
		200: '#e5e5e5',
		300: '#d4d4d4',
		400: '#a3a3a3',
		500: '#737373',
		600: '#525252',
		700: '#404040',
		800: '#262626',
		900: '#171717'
	}
} as const;

/**
 * Typography scale and font configuration
 * Using premium Google Fonts for luxury brand experience
 */
export const TYPOGRAPHY = {
	fontFamilies: {
		// Inter: Modern, clean, highly legible sans-serif perfect for body text and UI
		sans: ['Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
		// Playfair Display: Elegant, sophisticated serif for headings and luxury feel
		serif: ['Playfair Display', 'Georgia', 'Times New Roman', 'serif'],
		// JetBrains Mono: Clean monospace for code and technical content
		mono: ['JetBrains Mono', 'Fira Code', 'Consolas', 'Courier New', 'monospace']
	},
	
	fontSizes: {
		xs: '0.75rem',    // 12px
		sm: '0.875rem',   // 14px
		base: '1rem',     // 16px
		lg: '1.125rem',   // 18px
		xl: '1.25rem',    // 20px
		'2xl': '1.5rem',  // 24px
		'3xl': '1.875rem', // 30px
		'4xl': '2.25rem', // 36px
		'5xl': '3rem',    // 48px
		'6xl': '3.75rem', // 60px
		'7xl': '4.5rem',  // 72px
		'8xl': '6rem',    // 96px
		'9xl': '8rem'     // 128px
	},
	
	fontWeights: {
		thin: '100',
		extralight: '200',
		light: '300',
		normal: '400',
		medium: '500',
		semibold: '600',
		bold: '700',
		extrabold: '800',
		black: '900'
	},
	
	lineHeights: {
		none: '1',
		tight: '1.25',
		snug: '1.375',
		normal: '1.5',
		relaxed: '1.625',
		loose: '2'
	},
	
	letterSpacing: {
		tighter: '-0.05em',
		tight: '-0.025em',
		normal: '0em',
		wide: '0.025em',
		wider: '0.05em',
		widest: '0.1em'
	}
} as const;

/**
 * Spacing scale for consistent layout
 */
export const SPACING = {
	0: '0px',
	px: '1px',
	0.5: '0.125rem',  // 2px
	1: '0.25rem',     // 4px
	1.5: '0.375rem',  // 6px
	2: '0.5rem',      // 8px
	2.5: '0.625rem',  // 10px
	3: '0.75rem',     // 12px
	3.5: '0.875rem',  // 14px
	4: '1rem',        // 16px
	5: '1.25rem',     // 20px
	6: '1.5rem',      // 24px
	7: '1.75rem',     // 28px
	8: '2rem',        // 32px
	9: '2.25rem',     // 36px
	10: '2.5rem',     // 40px
	11: '2.75rem',    // 44px
	12: '3rem',       // 48px
	14: '3.5rem',     // 56px
	16: '4rem',       // 64px
	20: '5rem',       // 80px
	24: '6rem',       // 96px
	28: '7rem',       // 112px
	32: '8rem',       // 128px
	36: '9rem',       // 144px
	40: '10rem',      // 160px
	44: '11rem',      // 176px
	48: '12rem',      // 192px
	52: '13rem',      // 208px
	56: '14rem',      // 224px
	60: '15rem',      // 240px
	64: '16rem',      // 256px
	72: '18rem',      // 288px
	80: '20rem',      // 320px
	96: '24rem'       // 384px
} as const;

/**
 * Border radius values
 */
export const BORDER_RADIUS = {
	none: '0px',
	sm: '0.125rem',   // 2px
	DEFAULT: '0.25rem', // 4px
	md: '0.375rem',   // 6px
	lg: '0.5rem',     // 8px
	xl: '0.75rem',    // 12px
	'2xl': '1rem',    // 16px
	'3xl': '1.5rem',  // 24px
	full: '9999px'
} as const;

/**
 * Shadow definitions
 */
export const SHADOWS = {
	sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
	DEFAULT: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
	md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
	lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
	xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
	'2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
	inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
	none: '0 0 #0000'
} as const;

/**
 * Animation timing and easing
 */
export const ANIMATION = {
	durations: {
		75: '75ms',
		100: '100ms',
		150: '150ms',
		200: '200ms',
		300: '300ms',
		500: '500ms',
		700: '700ms',
		1000: '1000ms'
	},
	
	timingFunctions: {
		linear: 'linear',
		in: 'cubic-bezier(0.4, 0, 1, 1)',
		out: 'cubic-bezier(0, 0, 0.2, 1)',
		'in-out': 'cubic-bezier(0.4, 0, 0.2, 1)'
	}
} as const;

/**
 * Breakpoint definitions for responsive design
 */
export const BREAKPOINTS = {
	sm: '640px',
	md: '768px',
	lg: '1024px',
	xl: '1280px',
	'2xl': '1536px'
} as const;

/**
 * Z-index scale for layering
 */
export const Z_INDEX = {
	auto: 'auto',
	0: '0',
	10: '10',
	20: '20',
	30: '30',
	40: '40',
	50: '50',
	dropdown: '1000',
	sticky: '1020',
	fixed: '1030',
	modal: '1040',
	popover: '1050',
	tooltip: '1060',
	toast: '1070'
} as const;

/**
 * Component size variants
 */
export const COMPONENT_SIZES = {
	xs: {
		padding: SPACING[2],
		fontSize: TYPOGRAPHY.fontSizes.xs,
		height: '1.5rem'
	},
	sm: {
		padding: SPACING[3],
		fontSize: TYPOGRAPHY.fontSizes.sm,
		height: '2rem'
	},
	md: {
		padding: SPACING[4],
		fontSize: TYPOGRAPHY.fontSizes.base,
		height: '2.5rem'
	},
	lg: {
		padding: SPACING[6],
		fontSize: TYPOGRAPHY.fontSizes.lg,
		height: '3rem'
	},
	xl: {
		padding: SPACING[8],
		fontSize: TYPOGRAPHY.fontSizes.xl,
		height: '3.5rem'
	}
} as const;

/**
 * Brand-specific design tokens
 */
export const BRAND = {
	// Primary brand colors
	primary: COLORS.earth[600],
	primaryHover: COLORS.earth[700],
	primaryLight: COLORS.earth[100],
	
	// Secondary brand colors
	secondary: COLORS.gold[500],
	secondaryHover: COLORS.gold[600],
	secondaryLight: COLORS.gold[100],
	
	// Accent colors
	accent: COLORS.purple[600],
	accentHover: COLORS.purple[700],
	accentLight: COLORS.purple[100],
	
	// Neutral colors
	text: COLORS.neutral[900],
	textMuted: COLORS.neutral[600],
	textLight: COLORS.neutral[400],
	
	// Background colors
	background: COLORS.neutral[50],
	backgroundAlt: COLORS.neutral[100],
	surface: '#ffffff',
	
	// Border colors
	border: COLORS.neutral[200],
	borderLight: COLORS.neutral[100],
	
	// Status colors
	success: '#10b981',
	warning: '#f59e0b',
	error: '#ef4444',
	info: '#3b82f6'
} as const;

/**
 * Content width constraints
 */
export const CONTENT_WIDTH = {
	xs: '20rem',      // 320px
	sm: '24rem',      // 384px
	md: '28rem',      // 448px
	lg: '32rem',      // 512px
	xl: '36rem',      // 576px
	'2xl': '42rem',   // 672px
	'3xl': '48rem',   // 768px
	'4xl': '56rem',   // 896px
	'5xl': '64rem',   // 1024px
	'6xl': '72rem',   // 1152px
	'7xl': '80rem',   // 1280px
	full: '100%',
	screen: '100vw'
} as const;
