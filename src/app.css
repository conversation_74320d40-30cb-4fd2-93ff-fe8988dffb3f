/* ===== GOOGLE FONTS IMPORT ===== */
/* Importing premium fonts for St. Andrews Towers luxury brand experience */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

@import "tailwindcss";
@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/typography';

/* ===== ST. ANDREWS TOWERS DESIGN SYSTEM ===== */

/* Custom Properties - Design Tokens */
@theme {
	/* African Heritage Color Palette */
	--color-earth-50: #fdf8f3;
	--color-earth-100: #f9ede0;
	--color-earth-200: #f2d7bf;
	--color-earth-300: #e8bb94;
	--color-earth-400: #dc9567;
	--color-earth-500: #d17a47;
	--color-earth-600: #c3633c;
	--color-earth-700: #a24f34;
	--color-earth-800: #834131;
	--color-earth-900: #6b3729;

	/* Premium Gold Accents */
	--color-gold-50: #fffbeb;
	--color-gold-100: #fef3c7;
	--color-gold-200: #fde68a;
	--color-gold-300: #fcd34d;
	--color-gold-400: #fbbf24;
	--color-gold-500: #f59e0b;
	--color-gold-600: #d97706;
	--color-gold-700: #b45309;
	--color-gold-800: #92400e;
	--color-gold-900: #78350f;

	/* Women Empowerment Purple */
	--color-purple-50: #faf5ff;
	--color-purple-100: #f3e8ff;
	--color-purple-200: #e9d5ff;
	--color-purple-300: #d8b4fe;
	--color-purple-400: #c084fc;
	--color-purple-500: #a855f7;
	--color-purple-600: #9333ea;
	--color-purple-700: #7c3aed;
	--color-purple-800: #6b21a8;
	--color-purple-900: #581c87;

	/* Modern Neutrals */
	--color-neutral-50: #fafafa;
	--color-neutral-100: #f5f5f5;
	--color-neutral-200: #e5e5e5;
	--color-neutral-300: #d4d4d4;
	--color-neutral-400: #a3a3a3;
	--color-neutral-500: #737373;
	--color-neutral-600: #525252;
	--color-neutral-700: #404040;
	--color-neutral-800: #262626;
	--color-neutral-900: #171717;

	/* Typography Scale */
	--font-size-xs: 0.75rem;
	--font-size-sm: 0.875rem;
	--font-size-base: 1rem;
	--font-size-lg: 1.125rem;
	--font-size-xl: 1.25rem;
	--font-size-2xl: 1.5rem;
	--font-size-3xl: 1.875rem;
	--font-size-4xl: 2.25rem;
	--font-size-5xl: 3rem;
	--font-size-6xl: 3.75rem;
	--font-size-7xl: 4.5rem;
	--font-size-8xl: 6rem;
	--font-size-9xl: 8rem;

	/* Spacing Scale */
	--spacing-xs: 0.5rem;
	--spacing-sm: 0.75rem;
	--spacing-md: 1rem;
	--spacing-lg: 1.5rem;
	--spacing-xl: 2rem;
	--spacing-2xl: 2.5rem;
	--spacing-3xl: 3rem;
	--spacing-4xl: 4rem;
	--spacing-5xl: 5rem;
	--spacing-6xl: 6rem;

	/* Border Radius */
	--radius-sm: 0.25rem;
	--radius-md: 0.375rem;
	--radius-lg: 0.5rem;
	--radius-xl: 0.75rem;
	--radius-2xl: 1rem;
	--radius-3xl: 1.5rem;

	/* Shadows */
	--shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
	--shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
	--shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
	--shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
	--shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

	/* Animation Durations */
	--duration-fast: 150ms;
	--duration-normal: 300ms;
	--duration-slow: 500ms;
	--duration-slower: 750ms;

	/* Animation Easings */
	--ease-in: cubic-bezier(0.4, 0, 1, 1);
	--ease-out: cubic-bezier(0, 0, 0.2, 1);
	--ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
	--ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

	/* Premium Typography - Google Fonts */
	--font-sans:
		"Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
	--font-serif: "Playfair Display", Georgia, "Times New Roman", serif;
	--font-mono: "JetBrains Mono", "Fira Code", Consolas, "Courier New", monospace;
}

/* ===== COMPONENT STYLES ===== */

@layer components {
	/* Button Components */
	.btn {
		@apply inline-flex items-center justify-center gap-2 rounded-lg px-6 py-3 text-sm font-semibold transition-all duration-300 focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50;
	}

	.btn-primary {
		@apply btn bg-earth-600 hover:bg-earth-700 focus:ring-earth-500 transform text-white shadow-md hover:-translate-y-0.5 hover:shadow-lg;
	}

	.btn-secondary {
		@apply btn bg-gold-500 hover:bg-gold-600 focus:ring-gold-400 transform text-white shadow-md hover:-translate-y-0.5 hover:shadow-lg;
	}

	.btn-outline {
		@apply btn border-earth-600 text-earth-600 hover:bg-earth-600 focus:ring-earth-500 border-2 hover:text-white;
	}

	.btn-ghost {
		@apply btn text-earth-600 hover:bg-earth-50 focus:ring-earth-500;
	}

	.btn-lg {
		@apply px-8 py-4 text-base;
	}

	.btn-sm {
		@apply px-4 py-2 text-xs;
	}

	/* Card Components */
	.card {
		@apply rounded-xl border border-neutral-200 bg-white shadow-lg transition-all duration-300 hover:shadow-xl;
	}

	.card-elevated {
		@apply card transform shadow-2xl hover:-translate-y-1 hover:shadow-2xl;
	}

	.card-interactive {
		@apply card transform cursor-pointer transition-all duration-300 hover:-translate-y-2 hover:scale-105 hover:shadow-xl;
	}

	/* Typography Components - Premium Luxury Styling */
	.heading-hero {
		@apply text-5xl leading-tight font-bold tracking-tight text-neutral-900 md:text-6xl lg:text-7xl;
		font-family: var(--font-serif);
		font-weight: 700;
	}

	.heading-section {
		@apply text-3xl leading-tight font-bold tracking-tight text-neutral-900 md:text-4xl lg:text-5xl;
		font-family: var(--font-serif);
		font-weight: 600;
	}

	.heading-subsection {
		@apply text-2xl leading-snug font-semibold text-neutral-800 md:text-3xl;
		font-family: var(--font-serif);
		font-weight: 500;
	}

	.text-lead {
		@apply text-lg leading-relaxed text-neutral-600 md:text-xl;
		font-family: var(--font-sans);
		font-weight: 400;
	}

	.text-body {
		@apply text-base leading-relaxed text-neutral-700;
		font-family: var(--font-sans);
		font-weight: 400;
	}

	.text-caption {
		@apply text-sm leading-normal text-neutral-500;
		font-family: var(--font-sans);
		font-weight: 400;
	}

	/* Premium Text Variants */
	.text-luxury {
		font-family: var(--font-serif);
		font-weight: 600;
		letter-spacing: 0.025em;
	}

	.text-elegant {
		font-family: var(--font-serif);
		font-style: italic;
		font-weight: 400;
	}

	/* Gradient Text Effects */
	.text-gradient-primary {
		@apply from-earth-600 to-gold-500 bg-gradient-to-r bg-clip-text text-transparent;
	}

	.text-gradient-secondary {
		@apply to-earth-500 bg-gradient-to-r from-purple-600 bg-clip-text text-transparent;
	}

	/* Container Components */
	.container-section {
		@apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
	}

	.container-narrow {
		@apply mx-auto max-w-4xl px-4 sm:px-6 lg:px-8;
	}

	.container-wide {
		@apply mx-auto max-w-screen-2xl px-4 sm:px-6 lg:px-8;
	}

	/* Layout Components */
	.section-padding {
		@apply py-16 md:py-20 lg:py-24;
	}

	.section-padding-sm {
		@apply py-8 md:py-12 lg:py-16;
	}

	.section-padding-lg {
		@apply py-20 md:py-28 lg:py-32;
	}

	/* Form Components */
	.form-input {
		@apply focus:ring-earth-500 focus:border-earth-500 w-full rounded-lg border border-neutral-300 px-4 py-3 placeholder-neutral-400 transition-colors duration-200 focus:ring-2;
	}

	.form-textarea {
		@apply form-input resize-none;
	}

	.form-label {
		@apply mb-2 block text-sm font-medium text-neutral-700;
	}

	.form-error {
		@apply mt-1 text-sm text-red-600;
	}

	/* Animation Classes */
	.animate-fade-in {
		@apply animate-[fadeIn_0.6s_ease-out_forwards] opacity-0;
	}

	.animate-slide-up {
		@apply translate-y-8 animate-[slideUp_0.6s_ease-out_forwards] opacity-0;
	}

	.animate-scale-in {
		@apply scale-95 animate-[scaleIn_0.4s_ease-out_forwards] opacity-0;
	}

	/* Utility Classes */
	.backdrop-blur-glass {
		@apply border border-white/20 bg-white/80 backdrop-blur-md;
	}

	.gradient-overlay {
		@apply absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent;
	}

	.aspect-video {
		@apply aspect-[16/9];
	}

	.aspect-square {
		@apply aspect-[1/1];
	}

	.aspect-portrait {
		@apply aspect-[3/4];
	}
}

/* ===== KEYFRAME ANIMATIONS ===== */

@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

@keyframes slideUp {
	from {
		transform: translateY(2rem);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

@keyframes scaleIn {
	from {
		transform: scale(0.95);
		opacity: 0;
	}
	to {
		transform: scale(1);
		opacity: 1;
	}
}

@keyframes float {
	0%,
	100% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-10px);
	}
}

@keyframes pulse-glow {
	0%,
	100% {
		box-shadow: 0 0 20px rgba(209, 122, 71, 0.3);
	}
	50% {
		box-shadow: 0 0 40px rgba(209, 122, 71, 0.6);
	}
}

/* ===== GLOBAL STYLES ===== */

@layer base {
	* {
		box-sizing: border-box;
	}

	html {
		scroll-behavior: smooth;
		font-feature-settings: "cv02", "cv03", "cv04", "cv11";
	}

	body {
		@apply bg-neutral-50 text-neutral-900 antialiased;
		font-family: var(--font-sans);
		font-weight: 400;
		font-feature-settings: "cv02", "cv03", "cv04", "cv11";
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
		line-height: 1.6;
	}

	/* Custom Scrollbar */
	::-webkit-scrollbar {
		width: 8px;
	}

	::-webkit-scrollbar-track {
		@apply bg-neutral-100;
	}

	::-webkit-scrollbar-thumb {
		@apply bg-earth-400 rounded-full;
	}

	::-webkit-scrollbar-thumb:hover {
		@apply bg-earth-500;
	}

	/* Selection Styles */
	::selection {
		@apply bg-earth-200 text-earth-900;
	}

	::-moz-selection {
		@apply bg-earth-200 text-earth-900;
	}

	/* Focus Styles */
	:focus-visible {
		@apply ring-earth-500 ring-2 ring-offset-2 outline-none;
	}
}

/* Print Styles */
@media print {
	* {
		color: black !important;
		background: white !important;
	}

	.no-print {
		display: none !important;
	}
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
	*,
	*::before,
	*::after {
		animation-duration: 0.01ms !important;
		animation-iteration-count: 1 !important;
		transition-duration: 0.01ms !important;
		scroll-behavior: auto !important;
	}
}
